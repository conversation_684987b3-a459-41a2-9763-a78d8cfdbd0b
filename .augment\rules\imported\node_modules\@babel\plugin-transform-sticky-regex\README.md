---
type: "manual"
---

# @babel/plugin-transform-sticky-regex

> Compile ES2015 sticky regex to an ES5 RegExp constructor

See our website [@babel/plugin-transform-sticky-regex](https://babeljs.io/docs/babel-plugin-transform-sticky-regex) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-sticky-regex
```

or using yarn:

```sh
yarn add @babel/plugin-transform-sticky-regex --dev
```
