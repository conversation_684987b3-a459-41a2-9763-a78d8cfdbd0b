---
type: "manual"
---

# crypto-random-string [![Build Status](https://travis-ci.org/sindresorhus/crypto-random-string.svg?branch=master)](https://travis-ci.org/sindresorhus/crypto-random-string)

> Generate a [cryptographically strong](https://en.m.wikipedia.org/wiki/Strong_cryptography) random string

Can be useful for creating an identifier, slug, salt, fixture, etc.


## Install

```
$ npm install crypto-random-string
```


## Usage

```js
const cryptoRandomString = require('crypto-random-string');

cryptoRandomString(10);
//=> '2cf05d94db'
```


## API

### cryptoRandomString(length)

Returns a [`hex`](https://en.wikipedia.org/wiki/Hexadecimal) string.

#### length

Type: `number`

Length of the returned string.


## Related

- [random-int](https://github.com/sindresorhus/random-int) - Generate a random integer
- [random-float](https://github.com/sindresorhus/random-float) - Generate a random float
- [random-item](https://github.com/sindresorhus/random-item) - Get a random item from an array
- [random-boolean](https://github.com/arthurvr/random-boolean) - Get a random boolean
- [random-obj-key](https://github.com/sindresorhus/random-obj-key) - Get a random key from an object
- [random-obj-prop](https://github.com/sindresorhus/random-obj-prop) - Get a random property from an object
- [unique-random](https://github.com/sindresorhus/unique-random) - Generate random numbers that are consecutively unique


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
